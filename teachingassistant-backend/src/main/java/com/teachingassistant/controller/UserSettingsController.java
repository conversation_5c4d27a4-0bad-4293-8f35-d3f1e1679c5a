package com.teachingassistant.controller;

import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.UpdateThemeRequest;
import com.teachingassistant.dto.UpdateUserSettingsRequest;
import com.teachingassistant.entity.UserSettings;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.UserSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 用户设置控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/api/user/settings")
@RequiredArgsConstructor
public class UserSettingsController {
    
    private final UserSettingsService userSettingsService;
    
    /**
     * 获取当前用户设置
     */
    @GetMapping
    public Result<UserSettings> getUserSettings() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            UserSettings settings = userSettingsService.getUserSettings(userId);
            
            log.info("获取用户设置成功: {}", userId);
            return Result.success(settings);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户设置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取用户设置失败");
        }
    }
    
    /**
     * 更新用户设置
     */
    @PutMapping
    public Result<UserSettings> updateUserSettings(@Valid @RequestBody UpdateUserSettingsRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            UserSettings userSettings = new UserSettings();
            userSettings.setNotificationEnabled(request.getNotificationEnabled());
            userSettings.setTheme(request.getTheme());
            userSettings.setLanguage(request.getLanguage());
            userSettings.setClassReminder(request.getClassReminder());
            userSettings.setAppointmentReminder(request.getAppointmentReminder());
            userSettings.setSidebarCollapsed(request.getSidebarCollapsed());
            
            UserSettings updatedSettings = userSettingsService.updateUserSettings(userId, userSettings);
            
            log.info("更新用户设置成功: {}", userId);
            return Result.success(updatedSettings, "设置更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户设置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新用户设置失败");
        }
    }
    
    /**
     * 更新主题设置
     */
    @PutMapping("/theme")
    public Result<Void> updateTheme(@Valid @RequestBody UpdateThemeRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            userSettingsService.updateTheme(userId, request.getTheme());
            
            log.info("更新用户主题成功: {} -> {}", userId, request.getTheme());
            return Result.success("主题更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户主题失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新用户主题失败");
        }
    }
    
    /**
     * 更新通知设置
     */
    @PutMapping("/notification")
    public Result<Void> updateNotificationSettings(@RequestBody UpdateUserSettingsRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            userSettingsService.updateNotificationSettings(userId, 
                    request.getNotificationEnabled(),
                    request.getClassReminder(),
                    request.getAppointmentReminder());
            
            log.info("更新用户通知设置成功: {}", userId);
            return Result.success("通知设置更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户通知设置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新用户通知设置失败");
        }
    }
    
    /**
     * 更新界面设置
     */
    @PutMapping("/interface")
    public Result<Void> updateInterfaceSettings(@Valid @RequestBody UpdateUserSettingsRequest request) {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            userSettingsService.updateInterfaceSettings(userId,
                    request.getTheme(),
                    request.getLanguage(),
                    request.getSidebarCollapsed());
            
            log.info("更新用户界面设置成功: {}", userId);
            return Result.success("界面设置更新成功");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户界面设置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新用户界面设置失败");
        }
    }
    
    /**
     * 重置为默认设置
     */
    @PostMapping("/reset")
    public Result<UserSettings> resetToDefault() {
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED);
            }
            
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getUserId();
            
            // 删除现有设置
            if (userSettingsService.getUserSettings(userId) != null) {
                userSettingsService.deleteUserSettings(userId);
            }
            
            // 重新初始化默认设置
            UserSettings defaultSettings = userSettingsService.initDefaultSettings(userId);
            
            log.info("重置用户设置成功: {}", userId);
            return Result.success(defaultSettings, "设置已重置为默认值");
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重置用户设置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "重置用户设置失败");
        }
    }
}
