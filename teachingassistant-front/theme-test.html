<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题切换测试</title>
    <style>
        /* 基础颜色调色板 */
        :root {
            /* 基础白色系 */
            --vt-c-white: #ffffff;
            --vt-c-white-soft: #f8f9fa;
            --vt-c-white-mute: #f1f3f4;

            /* 基础黑色系 */
            --vt-c-black: #1a1a1a;
            --vt-c-black-soft: #2d2d2d;
            --vt-c-black-mute: #3a3a3a;

            /* 主色调 */
            --vt-c-primary: #667eea;
            --vt-c-primary-light: #8fa4f3;
            --vt-c-primary-dark: #4c63d2;

            /* 辅助色 */
            --vt-c-secondary: #764ba2;
            --vt-c-accent: #f093fb;
            
            /* 功能色 */
            --vt-c-success: #10b981;
            --vt-c-warning: #f59e0b;
            --vt-c-error: #ef4444;
            --vt-c-info: #3b82f6;

            /* 分割线颜色 */
            --vt-c-divider-light-1: rgba(60, 60, 60, 0.12);
            --vt-c-divider-light-2: rgba(60, 60, 60, 0.06);
            --vt-c-divider-dark-1: rgba(255, 255, 255, 0.18);
            --vt-c-divider-dark-2: rgba(255, 255, 255, 0.10);

            /* 文字颜色 */
            --vt-c-text-light-1: #2c3e50;
            --vt-c-text-light-2: #5a6c7d;
            --vt-c-text-light-3: #8492a6;
            --vt-c-text-dark-1: #ffffff;
            --vt-c-text-dark-2: rgba(255, 255, 255, 0.86);
            --vt-c-text-dark-3: rgba(255, 255, 255, 0.60);
        }

        /* 浅色主题 */
        :root,
        [data-theme="light"] {
            /* 背景色 */
            --color-background: var(--vt-c-white);
            --color-background-soft: var(--vt-c-white-soft);
            --color-background-mute: var(--vt-c-white-mute);
            --color-background-overlay: rgba(255, 255, 255, 0.95);

            /* 边框色 */
            --color-border: var(--vt-c-divider-light-2);
            --color-border-hover: var(--vt-c-divider-light-1);

            /* 文字色 */
            --color-heading: var(--vt-c-text-light-1);
            --color-text: var(--vt-c-text-light-1);
            --color-text-secondary: var(--vt-c-text-light-2);
            --color-text-muted: var(--vt-c-text-light-3);

            /* 卡片和容器 */
            --color-card-background: var(--vt-c-white);
            --color-card-border: var(--vt-c-divider-light-2);
            --color-card-shadow: rgba(0, 0, 0, 0.08);

            /* 侧边栏 */
            --color-sidebar-background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            --color-sidebar-text: var(--vt-c-text-light-1);
            --color-sidebar-text-active: var(--vt-c-primary);
            --color-sidebar-item-hover: rgba(102, 126, 234, 0.08);

            /* 头部导航 */
            --color-header-background: rgba(255, 255, 255, 0.95);
            --color-header-border: var(--vt-c-divider-light-2);

            /* 主要内容区 */
            --color-content-background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(241, 243, 244, 0.6) 100%);

            /* 主色调 */
            --color-primary: var(--vt-c-primary);
            --color-primary-light: var(--vt-c-primary-light);
            --color-primary-dark: var(--vt-c-primary-dark);

            /* 功能色 */
            --color-success: var(--vt-c-success);
            --color-warning: var(--vt-c-warning);
            --color-error: var(--vt-c-error);
            --color-info: var(--vt-c-info);
        }

        /* 深色主题 */
        [data-theme="dark"] {
            /* 背景色 */
            --color-background: var(--vt-c-black);
            --color-background-soft: var(--vt-c-black-soft);
            --color-background-mute: var(--vt-c-black-mute);
            --color-background-overlay: rgba(26, 26, 26, 0.95);

            /* 边框色 */
            --color-border: var(--vt-c-divider-dark-2);
            --color-border-hover: var(--vt-c-divider-dark-1);

            /* 文字色 */
            --color-heading: var(--vt-c-text-dark-1);
            --color-text: var(--vt-c-text-dark-1);
            --color-text-secondary: var(--vt-c-text-dark-2);
            --color-text-muted: var(--vt-c-text-dark-3);

            /* 卡片和容器 */
            --color-card-background: var(--vt-c-black-soft);
            --color-card-border: var(--vt-c-divider-dark-2);
            --color-card-shadow: rgba(0, 0, 0, 0.3);

            /* 侧边栏 */
            --color-sidebar-background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            --color-sidebar-text: var(--vt-c-text-dark-2);
            --color-sidebar-text-active: var(--vt-c-primary-light);
            --color-sidebar-item-hover: rgba(102, 126, 234, 0.15);

            /* 头部导航 */
            --color-header-background: rgba(45, 45, 45, 0.95);
            --color-header-border: var(--vt-c-divider-dark-2);

            /* 主要内容区 */
            --color-content-background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.6) 100%);

            /* 主色调 */
            --color-primary: var(--vt-c-primary-light);
            --color-primary-light: #a5b4fc;
            --color-primary-dark: var(--vt-c-primary);

            /* 功能色 */
            --color-success: #34d399;
            --color-warning: #fbbf24;
            --color-error: #f87171;
            --color-info: #60a5fa;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--color-background);
            color: var(--color-text);
            transition: all 0.3s ease;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background: var(--color-header-background);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--color-border);
            box-shadow: 0 4px 20px var(--color-card-shadow);
        }

        .theme-switcher {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .theme-btn {
            padding: 10px 20px;
            border: 2px solid var(--color-primary);
            background: transparent;
            color: var(--color-primary);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-btn:hover {
            background: var(--color-primary);
            color: white;
        }

        .theme-btn.active {
            background: var(--color-primary);
            color: white;
        }

        .demo-layout {
            display: flex;
            height: 400px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px var(--color-card-shadow);
        }

        .demo-sidebar {
            width: 200px;
            background: var(--color-sidebar-background);
            padding: 20px;
            border-right: 1px solid var(--color-border);
        }

        .demo-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .demo-header {
            height: 60px;
            background: var(--color-header-background);
            border-bottom: 1px solid var(--color-border);
            display: flex;
            align-items: center;
            padding: 0 20px;
        }

        .demo-content {
            flex: 1;
            background: var(--color-content-background);
            padding: 20px;
        }

        .demo-card {
            background: var(--color-card-background);
            border: 1px solid var(--color-card-border);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--color-card-shadow);
        }

        .demo-text {
            color: var(--color-text);
            margin-bottom: 10px;
        }

        .demo-text-secondary {
            color: var(--color-text-secondary);
            font-size: 14px;
        }

        h1, h2, h3 {
            color: var(--color-heading);
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>助教系统主题切换演示</h1>
            <div class="theme-switcher">
                <button class="theme-btn" onclick="setTheme('light')">浅色模式</button>
                <button class="theme-btn active" onclick="setTheme('dark')">深色模式</button>
            </div>
        </div>

        <div class="demo-layout">
            <div class="demo-sidebar">
                <h3>侧边栏</h3>
                <div class="demo-text">导航菜单</div>
                <div class="demo-text-secondary">菜单项目</div>
            </div>
            <div class="demo-main">
                <div class="demo-header">
                    <h3>顶部导航栏</h3>
                </div>
                <div class="demo-content">
                    <div class="demo-card">
                        <h3>卡片标题</h3>
                        <div class="demo-text">这是主要内容文字，展示了浅色和深色主题的效果。</div>
                        <div class="demo-text-secondary">这是次要文字，颜色相对较淡。</div>
                    </div>
                    <div class="demo-card">
                        <h3>另一个卡片</h3>
                        <div class="demo-text">主题切换会实时改变所有界面元素的颜色。</div>
                        <div class="demo-text-secondary">包括背景、文字、边框、阴影等。</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function setTheme(theme) {
            console.log('设置主题:', theme);
            document.documentElement.setAttribute('data-theme', theme);
            document.body.setAttribute('data-theme', theme);

            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // 找到对应的按钮并设置为活跃状态
            const targetBtn = Array.from(document.querySelectorAll('.theme-btn')).find(btn =>
                btn.textContent.includes(theme === 'light' ? '浅色' : '深色')
            );
            if (targetBtn) {
                targetBtn.classList.add('active');
            }

            // 保存到localStorage
            localStorage.setItem('theme', theme);

            // 显示当前主题信息
            console.log('HTML data-theme:', document.documentElement.getAttribute('data-theme'));
            console.log('Body data-theme:', document.body.getAttribute('data-theme'));
        }

        // 页面加载时恢复主题
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'dark';
            setTheme(savedTheme);
            
            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.includes(savedTheme === 'light' ? '浅色' : '深色')) {
                    btn.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
