<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题调试页面</title>
    <style>
        /* 复制base.css中的关键部分 */
        :root {
            --vt-c-white: #ffffff;
            --vt-c-white-soft: #f8f9fa;
            --vt-c-white-mute: #f1f3f4;
            --vt-c-black: #1a1a1a;
            --vt-c-black-soft: #2d2d2d;
            --vt-c-black-mute: #3a3a3a;
            --vt-c-primary: #667eea;
            --vt-c-text-light-1: #2c3e50;
            --vt-c-text-light-2: #5a6c7d;
            --vt-c-text-dark-1: #ffffff;
            --vt-c-text-dark-2: rgba(255, 255, 255, 0.86);
            --vt-c-divider-light-2: rgba(60, 60, 60, 0.06);
            --vt-c-divider-dark-2: rgba(255, 255, 255, 0.10);
        }

        /* 浅色主题 */
        :root,
        [data-theme="light"],
        html[data-theme="light"],
        body[data-theme="light"] {
            --color-background: var(--vt-c-white);
            --color-background-soft: var(--vt-c-white-soft);
            --color-text: var(--vt-c-text-light-1);
            --color-text-secondary: var(--vt-c-text-light-2);
            --color-border: var(--vt-c-divider-light-2);
            --color-card-background: var(--vt-c-white);
            --color-card-shadow: rgba(0, 0, 0, 0.08);
        }

        /* 深色主题 */
        [data-theme="dark"],
        html[data-theme="dark"],
        body[data-theme="dark"] {
            --color-background: var(--vt-c-black) !important;
            --color-background-soft: var(--vt-c-black-soft) !important;
            --color-text: var(--vt-c-text-dark-1) !important;
            --color-text-secondary: var(--vt-c-text-dark-2) !important;
            --color-border: var(--vt-c-divider-dark-2) !important;
            --color-card-background: var(--vt-c-black-soft) !important;
            --color-card-shadow: rgba(0, 0, 0, 0.3) !important;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--color-background) !important;
            color: var(--color-text) !important;
            transition: all 0.3s ease;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .debug-header {
            background: var(--color-card-background);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid var(--color-border);
            box-shadow: 0 4px 12px var(--color-card-shadow);
        }

        .debug-header h1 {
            color: var(--color-text);
            margin-bottom: 10px;
        }

        .theme-controls {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }

        .theme-btn {
            padding: 12px 24px;
            border: 2px solid var(--vt-c-primary);
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: var(--color-card-background);
            color: var(--color-text);
        }

        .theme-btn:hover {
            background: var(--vt-c-primary);
            color: white;
        }

        .theme-btn.active {
            background: var(--vt-c-primary);
            color: white;
        }

        .debug-info {
            background: var(--color-background-soft);
            border: 1px solid var(--color-border);
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            margin: 20px 0;
        }

        .debug-info h3 {
            color: var(--color-text);
            margin-bottom: 10px;
        }

        .debug-info p {
            color: var(--color-text-secondary);
            margin: 5px 0;
        }

        .test-card {
            background: var(--color-card-background);
            border: 1px solid var(--color-border);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 12px var(--color-card-shadow);
        }

        .test-card h3 {
            color: var(--color-text);
            margin-bottom: 15px;
        }

        .test-card p {
            color: var(--color-text);
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .test-card .secondary-text {
            color: var(--color-text-secondary);
            font-size: 14px;
        }
    </style>
</head>
<body data-theme="dark">
    <div class="container">
        <div class="debug-header">
            <h1>主题切换调试页面</h1>
            <p>用于测试主题切换功能是否正常工作</p>
            
            <div class="theme-controls">
                <button class="theme-btn" onclick="setTheme('light')">切换到浅色模式</button>
                <button class="theme-btn active" onclick="setTheme('dark')">切换到深色模式</button>
            </div>
        </div>

        <div class="debug-info">
            <h3>调试信息</h3>
            <p id="html-theme">HTML data-theme: </p>
            <p id="body-theme">Body data-theme: </p>
            <p id="computed-bg">计算后的背景色: </p>
            <p id="computed-text">计算后的文字色: </p>
        </div>

        <div class="test-card">
            <h3>测试卡片 1</h3>
            <p>这是主要文字内容，用于测试主题切换效果。</p>
            <p class="secondary-text">这是次要文字，颜色应该相对较淡。</p>
        </div>

        <div class="test-card">
            <h3>测试卡片 2</h3>
            <p>主题切换应该会改变背景色、文字色、边框色等。</p>
            <p class="secondary-text">包括卡片的阴影效果也会相应调整。</p>
        </div>
    </div>

    <script>
        function setTheme(theme) {
            console.log('设置主题:', theme);
            
            // 设置属性
            document.documentElement.setAttribute('data-theme', theme);
            document.body.setAttribute('data-theme', theme);
            
            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            const targetBtn = Array.from(document.querySelectorAll('.theme-btn')).find(btn => 
                btn.textContent.includes(theme === 'light' ? '浅色' : '深色')
            );
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
            
            // 更新调试信息
            updateDebugInfo();
        }

        function updateDebugInfo() {
            const htmlTheme = document.documentElement.getAttribute('data-theme') || 'none';
            const bodyTheme = document.body.getAttribute('data-theme') || 'none';
            
            const computedStyle = window.getComputedStyle(document.body);
            const bgColor = computedStyle.backgroundColor;
            const textColor = computedStyle.color;
            
            document.getElementById('html-theme').textContent = 'HTML data-theme: ' + htmlTheme;
            document.getElementById('body-theme').textContent = 'Body data-theme: ' + bodyTheme;
            document.getElementById('computed-bg').textContent = '计算后的背景色: ' + bgColor;
            document.getElementById('computed-text').textContent = '计算后的文字色: ' + textColor;
            
            console.log('调试信息:', {
                htmlTheme,
                bodyTheme,
                bgColor,
                textColor
            });
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            setTheme('dark');
            setInterval(updateDebugInfo, 1000);
        });
    </script>
</body>
</html>
