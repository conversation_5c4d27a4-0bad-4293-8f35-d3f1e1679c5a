/* 基础颜色调色板 */
:root {
  /* 基础白色系 */
  --vt-c-white: #ffffff;
  --vt-c-white-soft: #f8f9fa;
  --vt-c-white-mute: #f1f3f4;

  /* 基础黑色系 */
  --vt-c-black: #1a1a1a;
  --vt-c-black-soft: #2d2d2d;
  --vt-c-black-mute: #3a3a3a;

  /* 主色调 */
  --vt-c-primary: #667eea;
  --vt-c-primary-light: #8fa4f3;
  --vt-c-primary-dark: #4c63d2;

  /* 辅助色 */
  --vt-c-secondary: #764ba2;
  --vt-c-accent: #f093fb;

  /* 功能色 */
  --vt-c-success: #10b981;
  --vt-c-warning: #f59e0b;
  --vt-c-error: #ef4444;
  --vt-c-info: #3b82f6;

  /* 分割线颜色 */
  --vt-c-divider-light-1: rgba(60, 60, 60, 0.12);
  --vt-c-divider-light-2: rgba(60, 60, 60, 0.06);
  --vt-c-divider-dark-1: rgba(255, 255, 255, 0.18);
  --vt-c-divider-dark-2: rgba(255, 255, 255, 0.10);

  /* 文字颜色 */
  --vt-c-text-light-1: #2c3e50;
  --vt-c-text-light-2: #5a6c7d;
  --vt-c-text-light-3: #8492a6;
  --vt-c-text-dark-1: #ffffff;
  --vt-c-text-dark-2: rgba(255, 255, 255, 0.86);
  --vt-c-text-dark-3: rgba(255, 255, 255, 0.60);
}

/* 浅色主题 */
:root,
[data-theme="light"] {
  /* 背景色 */
  --color-background: var(--vt-c-white);
  --color-background-soft: var(--vt-c-white-soft);
  --color-background-mute: var(--vt-c-white-mute);
  --color-background-overlay: rgba(255, 255, 255, 0.95);

  /* 边框色 */
  --color-border: var(--vt-c-divider-light-2);
  --color-border-hover: var(--vt-c-divider-light-1);

  /* 文字色 */
  --color-heading: var(--vt-c-text-light-1);
  --color-text: var(--vt-c-text-light-1);
  --color-text-secondary: var(--vt-c-text-light-2);
  --color-text-muted: var(--vt-c-text-light-3);

  /* 卡片和容器 */
  --color-card-background: var(--vt-c-white);
  --color-card-border: var(--vt-c-divider-light-2);
  --color-card-shadow: rgba(0, 0, 0, 0.08);

  /* 侧边栏 */
  --color-sidebar-background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
  --color-sidebar-text: var(--vt-c-text-light-1);
  --color-sidebar-text-active: var(--vt-c-primary);
  --color-sidebar-item-hover: rgba(102, 126, 234, 0.08);

  /* 头部导航 */
  --color-header-background: rgba(255, 255, 255, 0.95);
  --color-header-border: var(--vt-c-divider-light-2);

  /* 主要内容区 */
  --color-content-background: linear-gradient(135deg, rgba(248, 249, 250, 0.8) 0%, rgba(241, 243, 244, 0.6) 100%);

  /* 主色调 */
  --color-primary: var(--vt-c-primary);
  --color-primary-light: var(--vt-c-primary-light);
  --color-primary-dark: var(--vt-c-primary-dark);

  /* 功能色 */
  --color-success: var(--vt-c-success);
  --color-warning: var(--vt-c-warning);
  --color-error: var(--vt-c-error);
  --color-info: var(--vt-c-info);

  --section-gap: 160px;
}

/* 深色主题 */
[data-theme="dark"] {
  /* 背景色 */
  --color-background: var(--vt-c-black);
  --color-background-soft: var(--vt-c-black-soft);
  --color-background-mute: var(--vt-c-black-mute);
  --color-background-overlay: rgba(26, 26, 26, 0.95);

  /* 边框色 */
  --color-border: var(--vt-c-divider-dark-2);
  --color-border-hover: var(--vt-c-divider-dark-1);

  /* 文字色 */
  --color-heading: var(--vt-c-text-dark-1);
  --color-text: var(--vt-c-text-dark-1);
  --color-text-secondary: var(--vt-c-text-dark-2);
  --color-text-muted: var(--vt-c-text-dark-3);

  /* 卡片和容器 */
  --color-card-background: var(--vt-c-black-soft);
  --color-card-border: var(--vt-c-divider-dark-2);
  --color-card-shadow: rgba(0, 0, 0, 0.3);

  /* 侧边栏 */
  --color-sidebar-background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  --color-sidebar-text: var(--vt-c-text-dark-2);
  --color-sidebar-text-active: var(--vt-c-primary-light);
  --color-sidebar-item-hover: rgba(102, 126, 234, 0.15);

  /* 头部导航 */
  --color-header-background: rgba(45, 45, 45, 0.95);
  --color-header-border: var(--vt-c-divider-dark-2);

  /* 主要内容区 */
  --color-content-background: linear-gradient(135deg, rgba(26, 26, 26, 0.8) 0%, rgba(45, 45, 45, 0.6) 100%);

  /* 主色调 */
  --color-primary: var(--vt-c-primary-light);
  --color-primary-light: #a5b4fc;
  --color-primary-dark: var(--vt-c-primary);

  /* 功能色 */
  --color-success: #34d399;
  --color-warning: #fbbf24;
  --color-error: #f87171;
  --color-info: #60a5fa;

  --section-gap: 160px;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  font-weight: normal;
}

body {
  min-height: 100vh;
  color: var(--color-text);
  background: var(--color-background);
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  font-family:
    Inter,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Fira Sans',
    'Droid Sans',
    'Helvetica Neue',
    sans-serif;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
