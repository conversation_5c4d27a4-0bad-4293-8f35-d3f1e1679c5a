package com.teachingassistant.service;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 验证码服务类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CaptchaService {

    private final DefaultKaptcha defaultKaptcha;

    @Autowired(required = false)
    private StringRedisTemplate stringRedisTemplate;

    // 内存存储作为Redis的备用方案
    private final ConcurrentHashMap<String, CaptchaData> memoryStore = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    /**
     * Redis中验证码的key前缀
     */
    private static final String CAPTCHA_KEY_PREFIX = "captcha:";
    
    /**
     * 验证码有效期（分钟）
     */
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;
    
    /**
     * 生成验证码
     * 
     * @return 包含验证码图片和key的结果
     */
    public CaptchaResult generateCaptcha() {
        try {
            // 生成验证码文本
            String captchaText = defaultKaptcha.createText();
            log.debug("生成验证码文本: {}", captchaText);
            
            // 生成验证码图片
            BufferedImage captchaImage = defaultKaptcha.createImage(captchaText);
            
            // 将图片转换为Base64字符串
            String imageBase64 = convertImageToBase64(captchaImage);
            
            // 生成唯一的key
            String captchaKey = UUID.randomUUID().toString();
            
            // 存储验证码文本（优先使用Redis，否则使用内存）
            storeCaptcha(captchaKey, captchaText.toLowerCase());
            
            log.info("验证码已生成并存储到Redis，key: {}, 有效期: {}分钟", captchaKey, CAPTCHA_EXPIRE_MINUTES);
            
            return new CaptchaResult(captchaKey, imageBase64);
            
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            throw new RuntimeException("生成验证码失败", e);
        }
    }
    
    /**
     * 验证验证码
     * 
     * @param captchaKey 验证码key
     * @param captchaText 用户输入的验证码
     * @return 验证结果
     */
    public boolean validateCaptcha(String captchaKey, String captchaText) {
        if (captchaKey == null || captchaText == null) {
            log.warn("验证码key或文本为空");
            return false;
        }
        
        try {
            String storedCaptcha = getCaptcha(captchaKey);

            if (storedCaptcha == null) {
                log.warn("验证码已过期或不存在，key: {}", captchaKey);
                return false;
            }

            // 验证码比较（不区分大小写）
            boolean isValid = storedCaptcha.equalsIgnoreCase(captchaText.trim());

            if (isValid) {
                // 验证成功后立即删除验证码
                removeCaptcha(captchaKey);
                log.info("验证码验证成功，key: {}", captchaKey);
            } else {
                log.warn("验证码验证失败，key: {}, 期望: {}, 实际: {}",
                    captchaKey, storedCaptcha, captchaText);
            }

            return isValid;
            
        } catch (Exception e) {
            log.error("验证验证码时发生错误，key: {}", captchaKey, e);
            return false;
        }
    }
    
    /**
     * 存储验证码
     */
    private void storeCaptcha(String captchaKey, String captchaText) {
        if (stringRedisTemplate != null) {
            try {
                String redisKey = CAPTCHA_KEY_PREFIX + captchaKey;
                stringRedisTemplate.opsForValue().set(redisKey, captchaText,
                    CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
                log.debug("验证码已存储到Redis，key: {}", captchaKey);
                return;
            } catch (Exception e) {
                log.warn("Redis存储失败，使用内存存储，key: {}, error: {}", captchaKey, e.getMessage());
            }
        }

        // 使用内存存储
        long expireTime = System.currentTimeMillis() + CAPTCHA_EXPIRE_MINUTES * 60 * 1000;
        memoryStore.put(captchaKey, new CaptchaData(captchaText, expireTime));

        // 定时清理过期数据
        scheduler.schedule(() -> memoryStore.remove(captchaKey), CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
        log.debug("验证码已存储到内存，key: {}", captchaKey);
    }

    /**
     * 获取验证码
     */
    private String getCaptcha(String captchaKey) {
        if (stringRedisTemplate != null) {
            try {
                String redisKey = CAPTCHA_KEY_PREFIX + captchaKey;
                return stringRedisTemplate.opsForValue().get(redisKey);
            } catch (Exception e) {
                log.warn("Redis获取失败，尝试内存获取，key: {}, error: {}", captchaKey, e.getMessage());
            }
        }

        // 从内存获取
        CaptchaData data = memoryStore.get(captchaKey);
        if (data != null) {
            if (data.expireTime > System.currentTimeMillis()) {
                return data.captchaText;
            } else {
                memoryStore.remove(captchaKey);
            }
        }
        return null;
    }

    /**
     * 删除验证码
     */
    private void removeCaptcha(String captchaKey) {
        if (stringRedisTemplate != null) {
            try {
                String redisKey = CAPTCHA_KEY_PREFIX + captchaKey;
                stringRedisTemplate.delete(redisKey);
                return;
            } catch (Exception e) {
                log.warn("Redis删除失败，尝试内存删除，key: {}, error: {}", captchaKey, e.getMessage());
            }
        }

        // 从内存删除
        memoryStore.remove(captchaKey);
    }

    /**
     * 将BufferedImage转换为Base64字符串
     *
     * @param image 图片对象
     * @return Base64字符串
     * @throws IOException IO异常
     */
    private String convertImageToBase64(BufferedImage image) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);
        byte[] imageBytes = baos.toByteArray();
        return Base64.getEncoder().encodeToString(imageBytes);
    }
    
    /**
     * 内存存储的验证码数据
     */
    private static class CaptchaData {
        private final String captchaText;
        private final long expireTime;

        public CaptchaData(String captchaText, long expireTime) {
            this.captchaText = captchaText;
            this.expireTime = expireTime;
        }
    }

    /**
     * 验证码结果类
     */
    public static class CaptchaResult {
        private final String captchaKey;
        private final String imageBase64;

        public CaptchaResult(String captchaKey, String imageBase64) {
            this.captchaKey = captchaKey;
            this.imageBase64 = imageBase64;
        }

        public String getCaptchaKey() {
            return captchaKey;
        }

        public String getImageBase64() {
            return imageBase64;
        }
    }
}
