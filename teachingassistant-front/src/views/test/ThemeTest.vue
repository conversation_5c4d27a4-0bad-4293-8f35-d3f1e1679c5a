<template>
  <div class="theme-test-container">
    <div class="test-header">
      <h1>主题切换测试页面</h1>
      <p>当前主题: {{ appStore.theme }}</p>
    </div>
    
    <div class="theme-controls">
      <button @click="switchToLight" class="theme-btn light">切换到浅色模式</button>
      <button @click="switchToDark" class="theme-btn dark">切换到深色模式</button>
    </div>
    
    <div class="test-content">
      <div class="test-card">
        <h3>测试卡片 1</h3>
        <p>这是主要文字内容，用于测试主题切换效果。</p>
        <p class="secondary-text">这是次要文字，颜色应该相对较淡。</p>
      </div>
      
      <div class="test-card">
        <h3>测试卡片 2</h3>
        <p>主题切换应该会改变背景色、文字色、边框色等。</p>
        <p class="secondary-text">包括卡片的阴影效果也会相应调整。</p>
      </div>
    </div>
    
    <div class="debug-info">
      <h4>调试信息</h4>
      <p>HTML data-theme: {{ htmlTheme }}</p>
      <p>Body data-theme: {{ bodyTheme }}</p>
      <p>Store theme: {{ appStore.theme }}</p>
      <p>LocalStorage theme: {{ localStorageTheme }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

const htmlTheme = ref('')
const bodyTheme = ref('')
const localStorageTheme = ref('')

const updateDebugInfo = () => {
  htmlTheme.value = document.documentElement.getAttribute('data-theme') || 'none'
  bodyTheme.value = document.body.getAttribute('data-theme') || 'none'
  localStorageTheme.value = localStorage.getItem('teaching_assistant_theme') || 'none'
}

const switchToLight = () => {
  console.log('切换到浅色模式')
  appStore.setTheme('light')
  setTimeout(updateDebugInfo, 100)
}

const switchToDark = () => {
  console.log('切换到深色模式')
  appStore.setTheme('dark')
  setTimeout(updateDebugInfo, 100)
}

onMounted(() => {
  updateDebugInfo()
  // 每秒更新一次调试信息
  setInterval(updateDebugInfo, 1000)
})
</script>

<style scoped>
.theme-test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--color-card-background);
  border-radius: 12px;
  border: 1px solid var(--color-card-border);
  box-shadow: 0 4px 12px var(--color-card-shadow);
}

.test-header h1 {
  color: var(--color-heading);
  margin-bottom: 10px;
}

.test-header p {
  color: var(--color-text-secondary);
  font-size: 16px;
}

.theme-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
}

.theme-btn {
  padding: 12px 24px;
  border: 2px solid var(--color-primary);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.theme-btn.light {
  background: #ffffff;
  color: #2c3e50;
  border-color: #667eea;
}

.theme-btn.light:hover {
  background: #667eea;
  color: white;
}

.theme-btn.dark {
  background: #1a1a1a;
  color: #ffffff;
  border-color: #8fa4f3;
}

.theme-btn.dark:hover {
  background: #8fa4f3;
  color: #1a1a1a;
}

.test-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.test-card {
  background: var(--color-card-background);
  border: 1px solid var(--color-card-border);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px var(--color-card-shadow);
  transition: all 0.3s ease;
}

.test-card h3 {
  color: var(--color-heading);
  margin-bottom: 15px;
}

.test-card p {
  color: var(--color-text);
  margin-bottom: 10px;
  line-height: 1.6;
}

.test-card .secondary-text {
  color: var(--color-text-secondary);
  font-size: 14px;
}

.debug-info {
  background: var(--color-background-soft);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 15px;
  font-family: monospace;
  font-size: 12px;
}

.debug-info h4 {
  color: var(--color-heading);
  margin-bottom: 10px;
}

.debug-info p {
  color: var(--color-text);
  margin: 5px 0;
}

@media (max-width: 768px) {
  .test-content {
    grid-template-columns: 1fr;
  }
  
  .theme-controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
