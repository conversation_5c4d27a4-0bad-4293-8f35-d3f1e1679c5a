<!DOCTYPE html>
<html lang="zh-CN" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深色主题优化测试</title>
    <style>
        /* 优化后的深色主题变量 */
        :root {
            --vt-c-black: #0f1419;
            --vt-c-black-soft: #1e2328;
            --vt-c-black-mute: #2a2d32;
            --vt-c-black-lighter: #363a41;
            --vt-c-text-dark-1: #f0f6fc;
            --vt-c-text-dark-2: #c9d1d9;
            --vt-c-text-dark-3: #8b949e;
            --vt-c-text-dark-4: #6e7681;
            --vt-c-divider-dark-1: rgba(240, 246, 252, 0.16);
            --vt-c-divider-dark-2: rgba(240, 246, 252, 0.08);
            --vt-c-primary: #58a6ff;
            --vt-c-primary-light: #79c0ff;
            --vt-c-success: #3fb950;
            --vt-c-warning: #d29922;
            --vt-c-error: #f85149;
        }

        [data-theme="dark"] {
            --color-background: var(--vt-c-black);
            --color-background-soft: var(--vt-c-black-soft);
            --color-background-mute: var(--vt-c-black-mute);
            --color-text: var(--vt-c-text-dark-1);
            --color-text-secondary: var(--vt-c-text-dark-2);
            --color-text-muted: var(--vt-c-text-dark-3);
            --color-heading: var(--vt-c-text-dark-1);
            --color-border: var(--vt-c-divider-dark-2);
            --color-border-hover: var(--vt-c-divider-dark-1);
            --color-card-background: var(--vt-c-black-soft);
            --color-card-border: var(--vt-c-divider-dark-1);
            --color-card-shadow: rgba(0, 0, 0, 0.4);
            --color-primary: var(--vt-c-primary);
            --color-primary-light: var(--vt-c-primary-light);
            --color-success: var(--vt-c-success);
            --color-warning: var(--vt-c-warning);
            --color-error: var(--vt-c-error);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--color-background);
            color: var(--color-text);
            transition: all 0.3s ease;
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            background: var(--color-card-background);
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid var(--color-card-border);
            box-shadow: 0 4px 12px var(--color-card-shadow);
        }

        .header h1 {
            color: var(--color-heading);
            margin-bottom: 10px;
            font-size: 28px;
            font-weight: 600;
        }

        .header p {
            color: var(--color-text-secondary);
            font-size: 16px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: var(--color-card-background);
            border: 1px solid var(--color-card-border);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px var(--color-card-shadow);
            transition: all 0.3s ease;
        }

        .card:hover {
            border-color: var(--color-border-hover);
            box-shadow: 0 8px 24px var(--color-card-shadow);
        }

        .card h3 {
            color: var(--color-heading);
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 600;
        }

        .card p {
            color: var(--color-text);
            margin-bottom: 12px;
        }

        .card .secondary-text {
            color: var(--color-text-secondary);
            font-size: 14px;
        }

        .card .muted-text {
            color: var(--color-text-muted);
            font-size: 13px;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: var(--color-primary);
            border-color: var(--color-primary);
            color: #ffffff;
        }

        .btn-primary:hover {
            background: var(--color-primary-light);
            border-color: var(--color-primary-light);
        }

        .btn-secondary {
            background: transparent;
            border-color: var(--color-border);
            color: var(--color-text);
        }

        .btn-secondary:hover {
            background: var(--color-background-soft);
            border-color: var(--color-primary);
            color: var(--color-primary);
        }

        .btn-success {
            background: var(--color-success);
            border-color: var(--color-success);
            color: #ffffff;
        }

        .btn-warning {
            background: var(--color-warning);
            border-color: var(--color-warning);
            color: #ffffff;
        }

        .btn-error {
            background: var(--color-error);
            border-color: var(--color-error);
            color: #ffffff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            color: var(--color-text);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--color-border);
            border-radius: 8px;
            background: var(--color-background-soft);
            color: var(--color-text);
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
        }

        .form-input::placeholder {
            color: var(--color-text-muted);
        }

        .status-indicators {
            display: flex;
            gap: 16px;
            margin-top: 20px;
        }

        .status {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success {
            background: rgba(63, 185, 80, 0.1);
            color: var(--color-success);
            border: 1px solid rgba(63, 185, 80, 0.2);
        }

        .status-warning {
            background: rgba(210, 153, 34, 0.1);
            color: var(--color-warning);
            border: 1px solid rgba(210, 153, 34, 0.2);
        }

        .status-error {
            background: rgba(248, 81, 73, 0.1);
            color: var(--color-error);
            border: 1px solid rgba(248, 81, 73, 0.2);
        }

        .divider {
            height: 1px;
            background: var(--color-border);
            margin: 30px 0;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }

        .color-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: var(--color-background-soft);
            border-radius: 8px;
            border: 1px solid var(--color-border);
        }

        .color-swatch {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: 1px solid var(--color-border);
        }

        .color-info {
            flex: 1;
        }

        .color-name {
            color: var(--color-text);
            font-size: 14px;
            font-weight: 500;
        }

        .color-value {
            color: var(--color-text-muted);
            font-size: 12px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>深色主题优化测试</h1>
            <p>测试优化后的深色主题颜色对比度和可读性</p>
        </div>

        <div class="grid">
            <div class="card">
                <h3>文字层级测试</h3>
                <p>这是主要文字内容，应该具有最高的对比度和可读性。</p>
                <p class="secondary-text">这是次要文字，用于辅助信息显示，对比度适中。</p>
                <p class="muted-text">这是弱化文字，用于不重要的提示信息。</p>
            </div>

            <div class="card">
                <h3>按钮组件测试</h3>
                <p>测试各种按钮在深色主题下的显示效果：</p>
                <div class="button-group">
                    <button class="btn btn-primary">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                    <button class="btn btn-success">成功按钮</button>
                    <button class="btn btn-warning">警告按钮</button>
                    <button class="btn btn-error">错误按钮</button>
                </div>
            </div>

            <div class="card">
                <h3>表单组件测试</h3>
                <div class="form-group">
                    <label class="form-label">用户名</label>
                    <input type="text" class="form-input" placeholder="请输入用户名" value="测试用户">
                </div>
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" class="form-input" placeholder="请输入邮箱地址">
                </div>
            </div>

            <div class="card">
                <h3>状态指示器测试</h3>
                <p>不同状态的颜色显示效果：</p>
                <div class="status-indicators">
                    <span class="status status-success">成功</span>
                    <span class="status status-warning">警告</span>
                    <span class="status status-error">错误</span>
                </div>
            </div>
        </div>

        <div class="divider"></div>

        <div class="card">
            <h3>颜色调色板</h3>
            <p>当前深色主题使用的主要颜色：</p>
            <div class="color-palette">
                <div class="color-item">
                    <div class="color-swatch" style="background: #0f1419;"></div>
                    <div class="color-info">
                        <div class="color-name">主背景色</div>
                        <div class="color-value">#0f1419</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #1e2328;"></div>
                    <div class="color-info">
                        <div class="color-name">卡片背景</div>
                        <div class="color-value">#1e2328</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #f0f6fc;"></div>
                    <div class="color-info">
                        <div class="color-name">主要文字</div>
                        <div class="color-value">#f0f6fc</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #c9d1d9;"></div>
                    <div class="color-info">
                        <div class="color-name">次要文字</div>
                        <div class="color-value">#c9d1d9</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #58a6ff;"></div>
                    <div class="color-info">
                        <div class="color-name">主色调</div>
                        <div class="color-value">#58a6ff</div>
                    </div>
                </div>
                <div class="color-item">
                    <div class="color-swatch" style="background: #3fb950;"></div>
                    <div class="color-info">
                        <div class="color-name">成功色</div>
                        <div class="color-value">#3fb950</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
