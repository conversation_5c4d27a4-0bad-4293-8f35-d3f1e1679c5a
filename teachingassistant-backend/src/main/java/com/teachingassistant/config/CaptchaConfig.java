package com.teachingassistant.config;

import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * 验证码配置类
 * 
 * <AUTHOR> Assistant System
 */
@Configuration
public class CaptchaConfig {
    
    @Bean
    public DefaultKaptcha defaultKaptcha() {
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        Properties properties = new Properties();
        
        // 验证码图片边框
        properties.setProperty("kaptcha.border", "yes");
        properties.setProperty("kaptcha.border.color", "105,179,90");
        
        // 验证码文本字符颜色
        properties.setProperty("kaptcha.textproducer.font.color", "blue");
        
        // 验证码图片宽度
        properties.setProperty("kaptcha.image.width", "120");
        
        // 验证码图片高度
        properties.setProperty("kaptcha.image.height", "40");
        
        // 验证码文本字符大小
        properties.setProperty("kaptcha.textproducer.font.size", "30");
        
        // 验证码文本字符间距
        properties.setProperty("kaptcha.textproducer.char.space", "3");
        
        // 验证码文本字符长度
        properties.setProperty("kaptcha.textproducer.char.length", "4");
        
        // 验证码文本字体样式
        properties.setProperty("kaptcha.textproducer.font.names", "宋体,楷体,微软雅黑");
        
        // 验证码噪点颜色
        properties.setProperty("kaptcha.noise.color", "white");
        
        // 验证码干扰实现类
        properties.setProperty("kaptcha.noise.impl", "com.google.code.kaptcha.impl.DefaultNoise");
        
        // 验证码背景颜色渐变，开始颜色
        properties.setProperty("kaptcha.background.color.from", "lightGray");
        
        // 验证码背景颜色渐变，结束颜色
        properties.setProperty("kaptcha.background.color.to", "white");
        
        // 验证码文本字符内容范围
        properties.setProperty("kaptcha.textproducer.char.string", "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        
        Config config = new Config(properties);
        defaultKaptcha.setConfig(config);
        
        return defaultKaptcha;
    }
}
